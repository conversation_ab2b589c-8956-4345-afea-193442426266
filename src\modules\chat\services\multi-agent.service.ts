import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  AgentCoordinatorService,
  HrmAgentService,
  ProjectAgentService,
  AnalyticsAgentService,
  ReportsAgentService,
  CommunicationAgentService,
  DocumentAgentService,
  GeneralAgentService,
  AgentConfig,
  AgentResponse,
} from './agents';

/**
 * Multi-Agent Service - Quản lý hệ thống multi-agent
 * Thay thế cho business-tools để giải quyết vấn đề giới hạn 128 tools của OpenAI
 */
@Injectable()
export class MultiAgentService implements OnModuleInit {
  private readonly logger = new Logger(MultiAgentService.name);

  constructor(
    private readonly coordinator: AgentCoordinatorService,
    private readonly hrmAgent: HrmAgentService,
    private readonly projectAgent: ProjectAgentService,
    private readonly analyticsAgent: AnalyticsAgentService,
    private readonly reportsAgent: ReportsAgentService,
    private readonly communicationAgent: CommunicationAgentService,
    private readonly documentAgent: DocumentAgentService,
    private readonly generalAgent: GeneralAgentService,
  ) {}

  /**
   * Khởi tạo tất cả agents khi module được load
   */
  async onModuleInit() {
    try {
      // Đăng ký tất cả agents với coordinator
      this.coordinator.registerAgent(this.hrmAgent);
      this.coordinator.registerAgent(this.projectAgent);
      this.coordinator.registerAgent(this.analyticsAgent);
      this.coordinator.registerAgent(this.reportsAgent);
      this.coordinator.registerAgent(this.communicationAgent);
      this.coordinator.registerAgent(this.documentAgent);
      this.coordinator.registerAgent(this.generalAgent);

      // Khởi tạo các agents
      await Promise.all([
        this.hrmAgent.initialize?.(),
        this.projectAgent.initialize?.(),
        this.analyticsAgent.initialize?.(),
        this.reportsAgent.initialize?.(),
        this.communicationAgent.initialize?.(),
        this.documentAgent.initialize?.(),
        this.generalAgent.initialize?.(),
      ]);

      const stats = this.coordinator.getAgentStats();
      this.logger.log(`Multi-Agent System đã khởi tạo với ${stats.totalAgents} agents`);
      
      // Log thống kê tools
      stats.agents.forEach(agent => {
        this.logger.debug(`${agent.name}: ${agent.toolsCount} tools`);
      });

    } catch (error) {
      this.logger.error(`Lỗi khởi tạo Multi-Agent System: ${error.message}`, error.stack);
    }
  }

  /**
   * Xử lý tin nhắn từ user với multi-agent system
   */
  async processMessage(
    message: string,
    config: AgentConfig,
    context?: any,
  ): Promise<AgentResponse> {
    try {
      this.logger.log(`Multi-Agent xử lý tin nhắn: "${message}"`);
      
      const response = await this.coordinator.processRequest(message, config, context);
      
      this.logger.debug(`Agent ${response.metadata?.agentUsed} đã xử lý với confidence: ${response.confidence}`);
      
      return response;
    } catch (error) {
      this.logger.error(`Lỗi trong Multi-Agent Service: ${error.message}`, error.stack);
      
      return {
        result: `Xin lỗi, hệ thống đang gặp sự cố: ${error.message}`,
        confidence: 0,
        error: error.message,
        metadata: {
          error: true,
          service: 'multi-agent',
        },
      };
    }
  }

  /**
   * Lấy thống kê về hệ thống multi-agent
   */
  getSystemStats(): any {
    const coordinatorStats = this.coordinator.getAgentStats();
    
    return {
      ...coordinatorStats,
      systemInfo: {
        totalTools: coordinatorStats.agents.reduce((sum, agent) => sum + agent.toolsCount, 0),
        maxToolsPerAgent: Math.max(...coordinatorStats.agents.map(agent => agent.toolsCount)),
        minToolsPerAgent: Math.min(...coordinatorStats.agents.map(agent => agent.toolsCount)),
        averageToolsPerAgent: coordinatorStats.agents.reduce((sum, agent) => sum + agent.toolsCount, 0) / coordinatorStats.totalAgents,
      },
      benefits: [
        'Giải quyết giới hạn 128 tools của OpenAI',
        'Phân chia chức năng rõ ràng theo domain',
        'Tăng hiệu suất xử lý',
        'Dễ dàng mở rộng và bảo trì',
        'Intelligent routing dựa trên intent',
      ],
    };
  }

  /**
   * Lấy danh sách agents và capabilities
   */
  getAgentCapabilities(): any {
    const agents = this.coordinator.getAgents();
    
    return agents.map(agent => ({
      name: agent.name,
      description: agent.description,
      priority: agent.priority,
      keywords: agent.keywords,
      toolsCount: agent.getTools().length,
      capabilities: this.getAgentSpecificCapabilities(agent.name),
    }));
  }

  /**
   * Lấy capabilities cụ thể của từng agent
   */
  private getAgentSpecificCapabilities(agentName: string): string[] {
    switch (agentName) {
      case 'hrm-agent':
        return [
          'Quản lý nhân viên (CRUD)',
          'Quản lý tài khoản user',
          'Cập nhật email, xóa user',
          'Quản lý phòng ban',
          'Chấm công và attendance',
          'Thống kê nhân sự',
        ];
      
      case 'project-agent':
        return [
          'Quản lý todo và task',
          'Quản lý dự án',
          'Gantt chart và timeline',
          'Collaboration và comment',
          'File attachment',
          'Project tracking',
        ];
      
      case 'analytics-agent':
        return [
          'Thống kê tổng quan',
          'Báo cáo hiệu suất',
          'OKR management',
          'Dashboard và visualization',
          'Data analysis',
        ];

      case 'reports-agent':
        return [
          'Tạo báo cáo Excel',
          'Xuất danh sách nhân viên',
          'Báo cáo chấm công chi tiết',
          'Thống kê nhân sự với biểu đồ',
          'Download links tự động',
          'Professional Excel styling',
        ];
      
      case 'communication-agent':
        return [
          'Gửi email và notification',
          'Quản lý sự kiện',
          'Calendar và meeting',
          'Alert và reminder',
        ];
      
      case 'document-agent':
        return [
          'Upload/download file',
          'Document sharing',
          'Access control',
          'File organization',
        ];
      
      case 'general-agent':
        return [
          'Hướng dẫn sử dụng',
          'Thông tin chung',
          'Tag management',
          'Fallback support',
        ];
      
      default:
        return [];
    }
  }

  /**
   * Test routing cho một tin nhắn
   */
  async testRouting(message: string): Promise<any> {
    const intent = await this.coordinator.analyzeIntent(message);
    const routing = await this.coordinator.routeToAgent(intent, message);
    
    return {
      message,
      intent,
      routing,
      selectedAgent: this.coordinator.getAgents().find(a => a.name === routing.selectedAgent),
    };
  }

  /**
   * Dọn dẹp tài nguyên khi shutdown
   */
  async onModuleDestroy() {
    try {
      await Promise.all([
        this.hrmAgent.cleanup?.(),
        this.projectAgent.cleanup?.(),
        this.analyticsAgent.cleanup?.(),
        this.reportsAgent.cleanup?.(),
        this.communicationAgent.cleanup?.(),
        this.documentAgent.cleanup?.(),
        this.generalAgent.cleanup?.(),
      ]);
      
      this.logger.log('Multi-Agent System đã được dọn dẹp');
    } catch (error) {
      this.logger.error(`Lỗi khi dọn dẹp Multi-Agent System: ${error.message}`);
    }
  }
}
